"""
AI Gen Hub 文本生成API路由

提供文本生成相关的API端点，包括：
- 文本生成（传统接口，保持向后兼容）
- 文本生成（优化版本接口，支持增强功能）
- 流式文本生成
- 支持的模型查询

版本更新：
- 添加 v2 API 端点支持优化版本请求
- 保持 v1 API 端点的向后兼容性
- 集成供应商兼容性检查和参数验证
"""

import json
from typing import Any, Dict, List, Union

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    OptimizedTextGenerationRequest,
    RequestAdapter,
)
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.services import TextGenerationService


router = APIRouter()


def get_text_service(request: Request) -> TextGenerationService:
    """获取文本生成服务依赖"""
    import logging
    logger = logging.getLogger(__name__)

    logger.debug("尝试获取文本生成服务...")

    # 检查应用状态是否存在
    if not hasattr(request.app, 'state'):
        logger.error("FastAPI应用状态未初始化")
        raise HTTPException(status_code=500, detail="应用状态未初始化")

    service = getattr(request.app.state, "text_service", None)
    if not service:
        logger.error("文本生成服务未在应用状态中找到")
        # 尝试列出所有可用的状态属性
        available_attrs = [attr for attr in dir(request.app.state) if not attr.startswith('_')]
        logger.error(f"可用的应用状态属性: {available_attrs}")
        raise HTTPException(status_code=500, detail="文本生成服务未初始化")

    logger.debug("文本生成服务获取成功")
    return service


def get_user_id(request: Request) -> str:
    """获取用户ID依赖"""
    user = getattr(request.state, "user", {})
    return user.get("user_id", "anonymous")


@router.post("/generate", response_model=Union[TextGenerationResponse, None])
async def generate_text(
    request_data: Union[TextGenerationRequest, Dict[str, Any]],
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """生成文本

    支持同步和流式两种模式：
    - 同步模式：返回完整的文本生成结果
    - 流式模式：返回Server-Sent Events流
    """
    import logging
    import asyncio
    import time

    logger = logging.getLogger(__name__)
    request_id = getattr(request.state, "request_id", f"req_{int(time.time())}")

    # 验证和转换请求格式
    if isinstance(request_data, dict):
        logger.info(f"收到字典格式请求: {request_id}")
        # 检查是否包含StreamConfig对象格式
        if isinstance(request_data.get("stream"), dict):
            logger.info("检测到StreamConfig对象格式，转换为布尔值")
            stream_config = request_data["stream"]
            request_data["stream"] = stream_config.get("enabled", False)

        # 创建TextGenerationRequest对象
        try:
            request_data = TextGenerationRequest(**request_data)
        except Exception as e:
            logger.error(f"请求格式转换失败: {e}")
            raise HTTPException(status_code=400, detail=f"请求格式错误: {str(e)}")

    logger.info(f"收到文本生成请求: request_id={request_id}, model={request_data.model}, stream={request_data.stream}")

    try:
        if request_data.stream:
            logger.info(f"处理流式请求: {request_id}")
            # 流式响应
            async def generate_stream():
                try:
                    logger.debug(f"开始生成流式响应: {request_id}")

                    # 添加超时保护
                    response_iter = await asyncio.wait_for(
                        text_service.generate_text(
                            request_data,
                            user_id=user_id,
                            request_id=request_id
                        ),
                        timeout=300.0  # 5分钟超时
                    )

                    chunk_count = 0
                    async for chunk in response_iter:
                        chunk_count += 1
                        if chunk_count % 10 == 0:
                            logger.debug(f"已发送 {chunk_count} 个流式响应块: {request_id}")

                        yield {
                            "event": "chunk",
                            "data": chunk.json()
                        }

                    logger.info(f"流式响应完成: {request_id}, 共发送 {chunk_count} 个块")

                    # 发送结束事件
                    yield {
                        "event": "done",
                        "data": "[DONE]"
                    }

                except asyncio.TimeoutError:
                    logger.error(f"流式请求超时: {request_id}")
                    yield {
                        "event": "error",
                        "data": json.dumps({"error": "请求处理超时，请稍后重试"})
                    }
                except Exception as e:
                    logger.error(f"流式请求处理失败: {request_id}, 错误: {e}", exc_info=True)
                    yield {
                        "event": "error",
                        "data": json.dumps({"error": f"处理失败: {str(e)}"})
                    }

            return EventSourceResponse(generate_stream())
        else:
            logger.info(f"处理同步请求: {request_id}")
            # 同步响应 - 添加超时保护
            start_time = time.time()

            response = await asyncio.wait_for(
                text_service.generate_text(
                    request_data,
                    user_id=user_id,
                    request_id=request_id
                ),
                timeout=300.0  # 5分钟超时
            )

            elapsed_time = time.time() - start_time
            logger.info(f"同步请求完成: {request_id}, 耗时: {elapsed_time:.2f}秒")

            return response

    except asyncio.TimeoutError:
        error_msg = f"请求处理超时: {request_id}"
        logger.error(error_msg)
        raise HTTPException(status_code=504, detail=error_msg)
    except AIGenHubException as e:
        logger.error(f"业务异常: {request_id}, 错误: {e}")
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        logger.error(f"未知异常: {request_id}, 错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/v2/generate", response_model=Union[TextGenerationResponse, None])
async def generate_text_v2(
    request_data: Union[OptimizedTextGenerationRequest, TextGenerationRequest, Dict[str, Any]],
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """生成文本（优化版本 API v2）

    这是新的优化版本API端点，提供以下增强功能：
    - 支持多种请求格式（优化版本、传统格式、字典格式）
    - 自动供应商兼容性检查和参数验证
    - 增强的错误处理和日志记录
    - 更好的参数组织和默认值设置

    Args:
        request_data: 文本生成请求（支持多种格式）
        request: FastAPI请求对象
        text_service: 文本生成服务
        user_id: 用户ID

    Returns:
        文本生成响应或流式响应
    """
    import logging
    import asyncio

    logger = logging.getLogger(__name__)
    request_id = getattr(request.state, "request_id", "unknown")

    try:
        logger.info(f"V2 API收到请求: request_id={request_id}, type={type(request_data).__name__}, user_id={user_id}")

        # 适配请求格式
        if isinstance(request_data, dict):
            logger.info("转换字典格式为优化版本")
            optimized_request = OptimizedTextGenerationRequest.from_legacy_request(request_data)
        elif isinstance(request_data, TextGenerationRequest):
            logger.info("转换传统格式为优化版本")
            optimized_request = OptimizedTextGenerationRequest.from_legacy_request(request_data.dict())
        elif isinstance(request_data, OptimizedTextGenerationRequest):
            logger.info("已经是优化版本格式")
            optimized_request = request_data
        else:
            error_msg = f"不支持的请求格式: {type(request_data)}"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)

        logger.info(f"请求适配完成: model={optimized_request.model}, stream={optimized_request.stream.enabled}")

        # 使用优化版本的文本生成服务（添加超时保护）
        try:
            if optimized_request.stream.enabled:
                logger.info("开始处理流式响应")
                # 流式响应
                response_iter = await asyncio.wait_for(
                    text_service.generate_text_optimized(
                        optimized_request,
                        user_id=user_id,
                        request_id=request_id
                    ),
                    timeout=300.0  # 5分钟超时
                )

                async def generate_stream():
                    try:
                        async for chunk in response_iter:
                            yield {
                                "event": "data",
                                "data": chunk.json() if hasattr(chunk, 'json') else chunk.model_dump_json()
                            }

                        # 发送结束标记
                        yield {
                            "event": "data",
                            "data": "[DONE]"
                        }
                    except Exception as e:
                        logger.error(f"流式响应生成失败: {e}")
                        yield {
                            "event": "error",
                            "data": json.dumps({"error": str(e)})
                        }

                return EventSourceResponse(generate_stream())
            else:
                logger.info("开始处理同步响应")
                # 同步响应（添加超时保护）
                response = await asyncio.wait_for(
                    text_service.generate_text_optimized(
                        optimized_request,
                        user_id=user_id,
                        request_id=request_id
                    ),
                    timeout=300.0  # 5分钟超时
                )
                logger.info("同步响应生成完成")
                return response

        except asyncio.TimeoutError:
            error_msg = "请求处理超时，请稍后重试"
            logger.error(f"V2 API超时: {error_msg}")
            raise HTTPException(status_code=504, detail=error_msg)
        except Exception as e:
            logger.error(f"V2 API处理异常: {e}", exc_info=True)
            raise

    except AIGenHubException as e:
        logger.error(f"V2 API业务异常: {e}")
        raise HTTPException(status_code=400, detail=e.to_dict())
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as e:
        # 处理参数验证错误（如functions/tools验证失败）
        error_msg = str(e)
        logger.error(f"V2 API参数验证错误: {error_msg}")

        # 提供友好的错误响应
        if "函数参数验证失败" in error_msg or "工具参数验证失败" in error_msg:
            detail = {
                "error_code": "PARAMETER_VALIDATION_ERROR",
                "message": "请求参数验证失败",
                "details": error_msg,
                "suggestions": [
                    "检查函数名称是否符合规范：以字母或下划线开头，只能包含字母数字、下划线、点、破折号",
                    "确保函数名称长度不超过64字符",
                    "移除空的或无效的函数/工具定义",
                    "确保每个函数都有有效的name字段"
                ]
            }
        else:
            detail = {
                "error_code": "VALIDATION_ERROR",
                "message": error_msg
            }

        raise HTTPException(status_code=400, detail=detail)
    except Exception as e:
        logger.error(f"V2 API未知异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/v2/validate", response_model=Dict[str, Any])
async def validate_request_compatibility(
    request_data: Union[OptimizedTextGenerationRequest, TextGenerationRequest, Dict[str, Any]],
    provider_name: str,
    request: Request
):
    """验证请求与指定供应商的兼容性（API v2）

    这个端点允许客户端在发送实际请求之前检查兼容性，
    有助于提前发现问题和获取优化建议。

    Args:
        request_data: 文本生成请求
        provider_name: 供应商名称
        request: FastAPI请求对象

    Returns:
        兼容性验证结果
    """
    try:
        # 适配请求格式
        optimized_request = RequestAdapter.adapt_request(request_data)

        # 执行兼容性验证
        validation_result = optimized_request.validate_for_provider(provider_name)

        # 获取供应商能力信息
        capabilities = OptimizedTextGenerationRequest.get_provider_capabilities(provider_name)

        return {
            "provider": provider_name,
            "validation": validation_result,
            "capabilities": capabilities,
            "is_compatible": len(validation_result["errors"]) == 0
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/models", response_model=Dict[str, List[str]])
async def get_supported_models(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取支持的文本生成模型列表

    返回按供应商分组的模型列表
    """
    try:
        models = await text_service.get_supported_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_service_stats(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取文本生成服务统计信息"""
    try:
        stats = await text_service.get_service_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 兼容OpenAI API格式的端点
@router.post("/chat/completions", response_model=Union[TextGenerationResponse, None])
async def chat_completions(
    request_data: TextGenerationRequest,
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """OpenAI兼容的聊天完成API
    
    提供与OpenAI Chat Completions API兼容的接口
    """
    return await generate_text(request_data, request, text_service, user_id)
