"""
AI Gen Hub 供应商基础适配器

提供所有供应商适配器的基础实现，包括：
- 通用的错误处理
- 请求/响应转换
- 重试机制
- 监控和日志记录
"""

import asyncio
import time
from abc import abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional, Union

import httpx
from tenacity import (
    AsyncRetrying,
    RetryError,
    stop_after_attempt,
    wait_exponential,
)

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.exceptions import (
    APIError,
    AuthenticationError,
    AuthorizationError,
    ModelNotSupportedError,
    ProviderUnavailableError,
    QuotaExceededError,
    RateLimitError,
    TimeoutError,
)
from ai_gen_hub.core.interfaces import (
    AIProvider,
    ImageGenerationRequest,
    ImageGenerationResponse,
    ModelType,
    ProviderInfo,
    ProviderStatus,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
)
from ai_gen_hub.core.logging import LoggerMixin
from ai_gen_hub.utils.key_manager import KeyManager


class BaseProvider(AIProvider, LoggerMixin):
    """供应商基础适配器类
    
    提供所有供应商适配器的通用功能实现，包括HTTP客户端管理、
    错误处理、重试机制等。具体的供应商适配器应该继承这个类。
    """
    
    def __init__(
        self,
        name: str,
        config: ProviderConfig,
        key_manager: KeyManager
    ):
        """初始化供应商适配器
        
        Args:
            name: 供应商名称
            config: 供应商配置
            key_manager: 密钥管理器
        """
        super().__init__(name, config.dict())
        self.config = config
        self.key_manager = key_manager
        
        # HTTP客户端
        self._client: Optional[httpx.AsyncClient] = None
        
        # 支持的模型类型
        self._supported_model_types: List[ModelType] = []
        
        # 支持的模型列表
        self._supported_models: List[str] = []
        
        # 模型映射（将标准模型名映射到供应商特定的模型名）
        self._model_mapping: Dict[str, str] = {}
    
    async def initialize(self) -> None:
        """初始化供应商连接和配置"""
        # 创建HTTP客户端
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.timeout),
            limits=httpx.Limits(max_connections=100, max_keepalive_connections=20),
            headers=self._get_default_headers()
        )
        
        # 更新状态
        self._status = ProviderStatus.HEALTHY
        
        self.logger.info(f"供应商 {self.name} 初始化完成")
    
    async def cleanup(self) -> None:
        """清理资源和连接"""
        if self._client:
            await self._client.aclose()
            self._client = None
        
        self.logger.info(f"供应商 {self.name} 资源已清理")
    
    async def health_check(self) -> bool:
        """执行健康检查"""
        try:
            # 尝试获取一个可用的API密钥
            key = await self.key_manager.get_key(self.name)
            if not key:
                self._status = ProviderStatus.UNHEALTHY
                return False
            
            # 执行具体的健康检查
            result = await self._perform_health_check(key.key)
            
            if result:
                self._status = ProviderStatus.HEALTHY
            else:
                self._status = ProviderStatus.DEGRADED
            
            self._last_health_check = time.time()
            return result
            
        except Exception as e:
            self.logger.error(f"健康检查失败", error=str(e))
            self._status = ProviderStatus.UNHEALTHY
            return False
    
    @abstractmethod
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行具体的健康检查
        
        Args:
            api_key: API密钥
            
        Returns:
            健康状态
        """
        pass
    
    async def get_provider_info(self) -> ProviderInfo:
        """获取供应商信息"""
        return ProviderInfo(
            name=self.name,
            version="1.0.0",
            status=self._status,
            models=self._supported_models,
            capabilities=self._supported_model_types,
            rate_limits={"requests_per_minute": self.config.rate_limit or 0},
            last_health_check=self._last_health_check
        )
    
    def supports_model_type(self, model_type: ModelType) -> bool:
        """检查是否支持指定的模型类型"""
        return model_type in self._supported_model_types
    
    def supports_model(self, model: str) -> bool:
        """检查是否支持指定的模型"""
        return model in self._supported_models or model in self._model_mapping
    
    def map_model_name(self, model: str) -> str:
        """映射模型名称到供应商特定的名称"""
        return self._model_mapping.get(model, model)
    
    async def generate_text(
        self,
        request: TextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本"""
        if not self.supports_model_type(ModelType.TEXT_GENERATION):
            raise ModelNotSupportedError(
                request.model,
                self.name,
                "该供应商不支持文本生成"
            )
        
        if not self.supports_model(request.model):
            raise ModelNotSupportedError(request.model, self.name)
        
        # 获取API密钥
        key = await self.key_manager.get_key(self.name)
        
        start_time = time.time()
        success = False
        error = None
        
        try:
            # 执行具体的文本生成
            result = await self._generate_text_impl(request, key.key)
            success = True
            return result
            
        except Exception as e:
            error = e
            raise
        finally:
            # 记录请求结果
            response_time = time.time() - start_time
            await self.key_manager.record_request(
                self.name, key, success, response_time, error
            )
    
    @abstractmethod
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """具体的文本生成实现"""
        pass
    
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """生成图像"""
        if not self.supports_model_type(ModelType.IMAGE_GENERATION):
            raise ModelNotSupportedError(
                request.model or "default",
                self.name,
                "该供应商不支持图像生成"
            )
        
        # 获取API密钥
        key = await self.key_manager.get_key(self.name)
        
        start_time = time.time()
        success = False
        error = None
        
        try:
            # 执行具体的图像生成
            result = await self._generate_image_impl(request, key.key)
            success = True
            return result
            
        except Exception as e:
            error = e
            raise
        finally:
            # 记录请求结果
            response_time = time.time() - start_time
            await self.key_manager.record_request(
                self.name, key, success, response_time, error
            )
    
    @abstractmethod
    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """具体的图像生成实现"""
        pass
    
    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认的HTTP头"""
        return {
            "User-Agent": f"AI-Gen-Hub/1.0.0 ({self.name})",
            "Content-Type": "application/json",
        }
    
    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> Union[httpx.Response, AsyncIterator[bytes]]:
        """发送HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            json_data: JSON数据
            stream: 是否流式请求

        Returns:
            响应对象或流式数据迭代器
        """
        if stream:
            # 流式请求，返回异步生成器
            return self._make_stream_request(method, url, headers, json_data)
        else:
            # 普通请求，返回响应对象
            return await self._make_normal_request(method, url, headers, json_data)

    async def _make_stream_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
    ) -> AsyncIterator[bytes]:
        """发送流式HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            json_data: JSON数据

        Yields:
            响应数据块
        """
        if not self._client:
            raise ProviderUnavailableError(self.name, "HTTP客户端未初始化")

        # 合并请求头
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)

        # 记录流式请求开始
        masked_url = self._mask_api_key_in_url(url)
        self.logger.debug(f"发送流式HTTP请求: {method} {masked_url}")
        if json_data:
            self.logger.debug(f"流式请求数据大小: {len(str(json_data))} 字符")

        start_time = time.time()
        chunk_count = 0
        total_bytes = 0

        try:
            # 使用重试机制
            async for attempt in AsyncRetrying(
                stop=stop_after_attempt(self.config.max_retries),
                wait=wait_exponential(
                    multiplier=self.config.retry_delay,
                    min=1,
                    max=60
                ),
                reraise=True
            ):
                with attempt:
                    if attempt.retry_state.attempt_number > 1:
                        self.logger.info(f"重试流式请求 (第{attempt.retry_state.attempt_number}次): {method} {masked_url}")

                    # 流式请求
                    async with self._client.stream(
                        method=method,
                        url=url,
                        headers=request_headers,
                        json=json_data
                    ) as response:
                        # 记录响应开始
                        self.logger.debug(f"流式响应开始: status={response.status_code}")
                        self._handle_response_errors(response)

                        async for chunk in response.aiter_bytes():
                            chunk_count += 1
                            total_bytes += len(chunk)

                            # 每100个chunk记录一次进度
                            if chunk_count % 100 == 0:
                                elapsed_time = time.time() - start_time
                                self.logger.debug(f"流式响应进度: {chunk_count} chunks, "
                                                f"{total_bytes} bytes, "
                                                f"{elapsed_time:.2f}s")

                            yield chunk

        except RetryError as e:
            # 重试失败
            elapsed_time = time.time() - start_time
            original_error = e.last_attempt.exception()

            self.logger.error(f"流式HTTP请求最终失败: {method} {masked_url}, "
                            f"耗时: {elapsed_time:.2f}s, "
                            f"已接收: {chunk_count} chunks ({total_bytes} bytes), "
                            f"重试次数: {self.config.max_retries}, "
                            f"错误: {original_error}")

            if isinstance(original_error, httpx.TimeoutException):
                raise TimeoutError(f"流式请求超时: {masked_url}")
            else:
                raise APIError(f"流式请求失败: {str(original_error)}")
        finally:
            # 记录流式请求完成
            elapsed_time = time.time() - start_time
            self.logger.debug(f"流式HTTP请求完成: {chunk_count} chunks, "
                            f"{total_bytes} bytes, "
                            f"耗时: {elapsed_time:.2f}s")

    async def _make_normal_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
    ) -> httpx.Response:
        """发送普通HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            json_data: JSON数据

        Returns:
            HTTP响应对象
        """
        if not self._client:
            raise ProviderUnavailableError(self.name, "HTTP客户端未初始化")

        # 合并请求头
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)

        # 记录请求开始
        masked_url = self._mask_api_key_in_url(url)
        self.logger.debug(f"发送HTTP请求: {method} {masked_url}")
        if json_data:
            self.logger.debug(f"请求数据大小: {len(str(json_data))} 字符")

        start_time = time.time()

        try:
            # 使用重试机制
            async for attempt in AsyncRetrying(
                stop=stop_after_attempt(self.config.max_retries),
                wait=wait_exponential(
                    multiplier=self.config.retry_delay,
                    min=1,
                    max=60
                ),
                reraise=True
            ):
                with attempt:
                    if attempt.retry_state.attempt_number > 1:
                        self.logger.info(f"重试请求 (第{attempt.retry_state.attempt_number}次): {method} {masked_url}")

                    # 普通请求
                    response = await self._client.request(
                        method=method,
                        url=url,
                        headers=request_headers,
                        json=json_data
                    )

                    # 记录响应信息
                    elapsed_time = time.time() - start_time
                    self.logger.debug(f"收到HTTP响应: status={response.status_code}, "
                                    f"size={len(response.content)} bytes, "
                                    f"time={elapsed_time:.2f}s")

                    self._handle_response_errors(response)
                    return response

        except RetryError as e:
            # 重试失败
            elapsed_time = time.time() - start_time
            original_error = e.last_attempt.exception()

            self.logger.error(f"HTTP请求最终失败: {method} {masked_url}, "
                            f"耗时: {elapsed_time:.2f}s, "
                            f"重试次数: {self.config.max_retries}, "
                            f"错误: {original_error}")

            if isinstance(original_error, httpx.TimeoutException):
                raise TimeoutError(f"请求超时: {masked_url}")
            else:
                raise APIError(f"请求失败: {str(original_error)}")

    def _mask_api_key_in_url(self, url: str) -> str:
        """在URL中遮蔽API密钥以便安全记录日志

        Args:
            url: 原始URL

        Returns:
            遮蔽API密钥后的URL
        """
        import re
        # 遮蔽URL中的API密钥参数
        return re.sub(r'([?&]key=)[^&]+', r'\1***', url)
    
    def _handle_response_errors(self, response: httpx.Response) -> None:
        """处理响应错误
        
        Args:
            response: HTTP响应
            
        Raises:
            相应的异常
        """
        if response.is_success:
            return
        
        status_code = response.status_code
        
        try:
            error_data = response.json()
        except Exception:
            # 对于流式响应，不能直接访问response.text
            try:
                error_data = {"error": response.text}
            except Exception:
                error_data = {"error": f"HTTP {status_code} Error"}
        
        error_message = self._extract_error_message(error_data)
        
        if status_code == 401:
            raise AuthenticationError(error_message)
        elif status_code == 403:
            raise AuthorizationError(error_message)
        elif status_code == 429:
            # 检查是否是配额超限
            if "quota" in error_message.lower() or "billing" in error_message.lower():
                raise QuotaExceededError(error_message)
            else:
                # 提取重试时间
                retry_after = response.headers.get("Retry-After")
                retry_after_seconds = int(retry_after) if retry_after else None
                raise RateLimitError(error_message, retry_after_seconds)
        elif status_code >= 500:
            raise APIError(
                error_message,
                status_code=status_code,
                response_data=error_data,
                retryable=True
            )
        else:
            raise APIError(
                error_message,
                status_code=status_code,
                response_data=error_data,
                retryable=False
            )
    
    def _extract_error_message(self, error_data: Dict[str, Any]) -> str:
        """从错误数据中提取错误消息
        
        Args:
            error_data: 错误数据
            
        Returns:
            错误消息
        """
        # 尝试不同的错误消息字段
        for field in ["message", "error", "detail", "error_description"]:
            if field in error_data:
                error_value = error_data[field]
                if isinstance(error_value, str):
                    return error_value
                elif isinstance(error_value, dict) and "message" in error_value:
                    return error_value["message"]
        
        return f"API错误: {error_data}"
