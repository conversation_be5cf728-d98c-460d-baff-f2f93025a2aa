"""
Hugging Face 供应商适配器 - 基于最新 Hugging Face Inference API 规范

实现 Hugging Face Inference API 的完整适配，支持大量开源模型。

主要功能：
- 文本生成：支持 Llama、Mistral、DeepSeek 等开源模型
- 图像生成：支持 FLUX、Stable Diffusion 等图像生成模型
- 多模态：支持视觉理解和语音识别模型
- OpenAI 兼容：使用 OpenAI 兼容的 API 格式
- 自动路由：自动选择最佳供应商和实例

支持的模型系列：
- 文本生成：meta-llama/Llama-3.1-8B-Instruct, deepseek-ai/DeepSeek-V3-0324
- 图像生成：black-forest-labs/FLUX.1-dev, stabilityai/stable-diffusion-3.5-large
- 多模态：meta-llama/Llama-3.2-11B-Vision-Instruct
- 语音识别：openai/whisper-large-v3

免费额度：免费用户每月$0.10，PRO用户每月$2.00

更新日期：2025-08-14
API 版本：v1 (OpenAI 兼容)
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager
from ai_gen_hub.core.compatibility import ProviderCompatibilityManager, ProviderType


class HuggingFaceProvider(BaseProvider):
    """Hugging Face 供应商适配器

    基于最新 Hugging Face Inference API 规范的完整实现，支持：

    核心功能：
    - 开源模型：支持大量高质量开源模型
    - OpenAI 兼容：使用标准的 OpenAI API 格式
    - 自动路由：智能选择最佳供应商实例
    - 多模态支持：文本、图像、语音等多种模态
    - 成本优化：免费额度和按需付费

    模型类型：
    - 文本生成：Llama 3.1/3.2、DeepSeek V3、Mistral 等
    - 图像生成：FLUX.1、Stable Diffusion 3.5 等
    - 视觉理解：Llama Vision、Qwen2-VL 等
    - 语音识别：Whisper Large V3 等

    配置选项：
    - temperature：控制输出随机性
    - max_tokens：限制输出长度
    - top_p：核采样参数
    - stream：流式输出支持
    """

    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化 Hugging Face 适配器

        Args:
            config: Hugging Face 配置对象，包含 API 设置
            key_manager: 密钥管理器，用于 API 密钥的安全管理
        """
        super().__init__("huggingface", config, key_manager)

        # 设置 Hugging Face Inference API 基础 URL（OpenAI 兼容）
        self.base_url = "https://router.huggingface.co/v1"

        # 当前支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,  # 文本生成
            ModelType.IMAGE_GENERATION, # 图像生成
        ]
        
        # 支持的文本生成模型列表 - 基于最新的 Hugging Face 模型库
        self._supported_text_models = [
            # Llama 系列 - Meta 的开源大模型
            "meta-llama/Llama-3.1-8B-Instruct",         # Llama 3.1 8B 指令微调版
            "meta-llama/Llama-3.1-70B-Instruct",        # Llama 3.1 70B 指令微调版
            "meta-llama/Llama-3.2-11B-Vision-Instruct", # Llama 3.2 多模态版本
            "meta-llama/Llama-3.2-3B-Instruct",         # Llama 3.2 轻量级版本
            
            # DeepSeek 系列 - 高性能中文模型
            "deepseek-ai/DeepSeek-V3-0324",              # DeepSeek V3 最新版本
            "deepseek-ai/deepseek-coder-33b-instruct",   # DeepSeek 代码专用模型
            
            # Mistral 系列 - 欧洲开源模型
            "mistralai/Mistral-7B-Instruct-v0.3",       # Mistral 7B 指令版
            "mistralai/Mixtral-8x7B-Instruct-v0.1",     # Mixtral 混合专家模型
            
            # Qwen 系列 - 阿里巴巴开源模型
            "Qwen/Qwen2.5-72B-Instruct",                # Qwen 2.5 72B 版本
            "Qwen/Qwen2-VL-72B-Instruct",               # Qwen 2 视觉语言模型
            
            # 其他优秀开源模型
            "microsoft/DialoGPT-large",                  # 微软对话模型
            "google/flan-t5-xxl",                       # Google T5 大模型
        ]
        
        # 支持的图像生成模型列表
        self._supported_image_models = [
            # FLUX 系列 - 最新的图像生成模型
            "black-forest-labs/FLUX.1-dev",             # FLUX.1 开发版
            "black-forest-labs/FLUX.1-schnell",         # FLUX.1 快速版
            
            # Stable Diffusion 系列
            "stabilityai/stable-diffusion-3.5-large",   # SD 3.5 大模型
            "stabilityai/stable-diffusion-xl-base-1.0", # SDXL 基础版
            
            # 其他图像生成模型
            "runwayml/stable-diffusion-v1-5",           # SD 1.5 经典版
            "CompVis/stable-diffusion-v1-4",            # SD 1.4 版本
        ]

        # 合并所有支持的模型
        self._supported_models = self._supported_text_models + self._supported_image_models

        # 初始化兼容性管理器
        self._compatibility_manager = ProviderCompatibilityManager()

        # 模型映射 - 将通用名称映射到具体的 Hugging Face 模型
        self._model_mapping = {
            # 通用别名映射
            "huggingface-latest": "meta-llama/Llama-3.1-70B-Instruct",  # 默认推荐最强模型
            "huggingface": "meta-llama/Llama-3.1-8B-Instruct",          # 简单别名映射到平衡模型
            "hf": "meta-llama/Llama-3.1-8B-Instruct",                   # 简短别名
            
            # 性能优化映射
            "huggingface-fast": "meta-llama/Llama-3.2-3B-Instruct",     # 快速模型别名
            "huggingface-best": "meta-llama/Llama-3.1-70B-Instruct",    # 最佳模型别名
            "huggingface-code": "deepseek-ai/deepseek-coder-33b-instruct", # 代码专用模型
            "huggingface-chinese": "deepseek-ai/DeepSeek-V3-0324",      # 中文优化模型
            
            # 图像生成映射
            "huggingface-image": "black-forest-labs/FLUX.1-dev",        # 默认图像生成模型
            "huggingface-flux": "black-forest-labs/FLUX.1-dev",         # FLUX 模型别名
            "huggingface-sd": "stabilityai/stable-diffusion-3.5-large", # SD 模型别名
            
            # 多模态映射
            "huggingface-vision": "meta-llama/Llama-3.2-11B-Vision-Instruct", # 视觉理解模型
            "huggingface-multimodal": "Qwen/Qwen2-VL-72B-Instruct",     # 多模态模型
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行 API 健康检查

        通过调用 models 端点来验证 API 密钥的有效性和服务可用性。
        Hugging Face 使用 OpenAI 兼容的 API 格式。

        Args:
            api_key: 要验证的 Hugging Face API 密钥（格式：hf_****）

        Returns:
            bool: 如果 API 可用且密钥有效则返回 True，否则返回 False
        """
        try:
            # 构建健康检查请求 - 使用 models 端点
            url = f"{self.base_url}/models"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            }
            
            response = await self._make_request("GET", url, headers=headers)
            return response.status_code == 200
            
        except Exception as e:
            # 记录健康检查失败的详细信息
            self.logger.warning(f"Hugging Face API 健康检查失败: {e}")
            return False
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成的核心逻辑

        使用 OpenAI 兼容的 API 格式调用 Hugging Face Inference API。
        自动处理模型名称映射、请求构建和响应解析。

        Args:
            request: 文本生成请求对象，包含模型、消息、参数等
            api_key: 有效的 Hugging Face API 密钥

        Returns:
            Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
                非流式请求返回完整响应，流式请求返回响应块的异步迭代器
        """
        # 构建符合 OpenAI 格式的请求数据
        request_data = self._build_openai_request(request)

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        if request.stream:
            # 流式生成：使用 chat/completions 端点的流式模式
            url = f"{self.base_url}/chat/completions"
            request_data["stream"] = True
            return self._handle_openai_stream_response(request, url, headers, request_data)
        else:
            # 非流式生成：使用 chat/completions 端点
            url = f"{self.base_url}/chat/completions"
            response = await self._make_request("POST", url, headers=headers, json_data=request_data)
            response_data = response.json()

            # 检查响应中是否有错误
            if "error" in response_data:
                error_msg = self._handle_openai_error(response_data)
                self.logger.error(f"Hugging Face API 返回错误: {error_msg}")
                # 根据错误类型抛出相应的异常
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)

            return self._parse_openai_response(response_data, request)
    
    def _build_openai_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建 OpenAI 兼容的请求数据

        由于 Hugging Face 使用 OpenAI 兼容的 API 格式，
        可以直接使用标准的 OpenAI 请求格式。

        Args:
            request: 文本生成请求对象

        Returns:
            Dict[str, Any]: OpenAI 格式的请求数据
        """
        # 映射模型名称（处理别名和向后兼容）
        model_name = self.map_model_name(request.model)
        
        # 构建基础请求数据
        request_data = {
            "model": model_name,
            "messages": [
                {
                    "role": msg.role.value,
                    "content": msg.content
                }
                for msg in request.messages
            ]
        }

        # 添加生成配置
        if request.max_tokens is not None:
            request_data["max_tokens"] = request.max_tokens
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        if request.top_p is not None:
            request_data["top_p"] = request.top_p
        if request.stop is not None:
            request_data["stop"] = request.stop

        # 添加工具配置（如果支持）
        if request.functions:
            request_data["functions"] = request.functions
            if request.function_call:
                request_data["function_call"] = request.function_call
        
        if request.tools:
            request_data["tools"] = request.tools
            if request.tool_choice:
                request_data["tool_choice"] = request.tool_choice
        
        return request_data

    def _parse_openai_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析 OpenAI 格式的响应数据

        由于 Hugging Face 使用 OpenAI 兼容的格式，
        可以直接解析标准的 OpenAI 响应。

        Args:
            response_data: Hugging Face API 返回的响应数据
            request: 原始请求对象

        Returns:
            TextGenerationResponse: 标准化的响应对象
        """
        # 直接使用 OpenAI 格式的响应数据
        choices = []
        for choice_data in response_data.get("choices", []):
            message_data = choice_data.get("message", {})

            # 构建消息对象
            message = Message(
                role=MessageRole(message_data.get("role", "assistant")),
                content=message_data.get("content", ""),
                tool_calls=message_data.get("tool_calls")
            )

            # 构建选择对象
            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                message=message,
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)

        # 构建使用统计
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )

        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            object=response_data.get("object", "chat.completion"),
            created=response_data.get("created", int(time.time())),
            model=response_data.get("model", request.model),
            choices=choices,
            usage=usage
        )

    async def _handle_openai_stream_response(
        self,
        request: TextGenerationRequest,
        url: str,
        headers: Dict[str, str],
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理 OpenAI 格式的流式响应

        处理 Hugging Face API 的 SSE 流式响应，格式与 OpenAI 兼容。

        Args:
            request: 原始请求对象
            url: API 端点 URL
            headers: 请求头
            request_data: 请求数据

        Yields:
            TextGenerationStreamChunk: 流式响应块
        """
        try:
            async with self.http_client.stream("POST", url, headers=headers, json=request_data) as response:
                if response.status_code != 200:
                    error_data = await response.aread()
                    error_msg = f"Hugging Face API 流式请求失败: {response.status_code} - {error_data.decode()}"
                    self.logger.error(error_msg)
                    from ai_gen_hub.core.exceptions import APIError
                    raise APIError(error_msg)

                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk

                    # 处理 SSE 格式的数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)

                        if line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀

                            # 跳过空行和结束标记
                            if not data_str.strip() or data_str.strip() == "[DONE]":
                                continue

                            try:
                                data = json.loads(data_str)
                                stream_chunk = self._parse_openai_stream_chunk(data, request)
                                if stream_chunk:
                                    yield stream_chunk
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"解析流式响应数据失败: {e}, 数据: {data_str}")
                                continue

        except Exception as e:
            self.logger.error(f"处理 Hugging Face 流式响应时发生错误: {e}")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(f"流式响应处理失败: {e}")

    def _parse_openai_stream_chunk(
        self,
        chunk_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> Optional[TextGenerationStreamChunk]:
        """解析 OpenAI 格式的流式响应块

        Args:
            chunk_data: Hugging Face 流式响应块数据
            request: 原始请求对象

        Returns:
            Optional[TextGenerationStreamChunk]: 标准化的流式响应块，如果无效则返回 None
        """
        choices = []
        for choice_data in chunk_data.get("choices", []):
            delta_data = choice_data.get("delta", {})

            # 构建增量消息对象
            delta = Message(
                role=MessageRole(delta_data.get("role", "assistant")) if delta_data.get("role") else None,
                content=delta_data.get("content", ""),
                tool_calls=delta_data.get("tool_calls")
            )

            # 构建选择对象
            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                delta=delta,
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)

        if choices:
            return TextGenerationStreamChunk(
                id=chunk_data.get("id", str(uuid4())),
                object=chunk_data.get("object", "chat.completion.chunk"),
                created=chunk_data.get("created", int(time.time())),
                model=chunk_data.get("model", request.model),
                choices=choices
            )

        return None

    def _handle_openai_error(self, response_data: Dict[str, Any]) -> str:
        """处理 OpenAI 格式的错误

        解析 Hugging Face API 返回的错误信息（OpenAI 兼容格式）。

        Args:
            response_data: 包含错误信息的响应数据

        Returns:
            str: 格式化的错误消息
        """
        error_info = response_data.get("error", {})

        if isinstance(error_info, dict):
            error_msg = error_info.get("message", "未知错误")
            error_type = error_info.get("type", "unknown")
            error_code = error_info.get("code", "unknown")
            return f"Hugging Face API 错误 ({error_type}/{error_code}): {error_msg}"
        else:
            return f"Hugging Face API 错误: {error_info}"

    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """图像生成实现

        使用 Hugging Face 的图像生成模型（如 FLUX.1、Stable Diffusion）
        生成高质量图像。

        Args:
            request: 图像生成请求
            api_key: API 密钥

        Returns:
            ImageGenerationResponse: 图像生成响应
        """
        # 映射模型名称
        model_name = self.map_model_name(request.model)

        # 确保使用的是图像生成模型
        if model_name not in self._supported_image_models:
            # 如果不是图像模型，使用默认的图像生成模型
            model_name = "black-forest-labs/FLUX.1-dev"

        # 构建图像生成请求
        request_data = {
            "model": model_name,
            "prompt": request.prompt,
            "n": request.n or 1,
            "size": request.size or "1024x1024",
        }

        # 添加可选参数
        if hasattr(request, 'quality') and request.quality:
            request_data["quality"] = request.quality
        if hasattr(request, 'style') and request.style:
            request_data["style"] = request.style

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        # 发送请求
        url = f"{self.base_url}/images/generations"
        response = await self._make_request("POST", url, headers=headers, json_data=request_data)
        response_data = response.json()

        # 检查错误
        if "error" in response_data:
            error_msg = self._handle_openai_error(response_data)
            self.logger.error(f"Hugging Face 图像生成 API 返回错误: {error_msg}")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(error_msg)

        # 解析响应
        return self._parse_image_response(response_data, request)

    def _parse_image_response(
        self,
        response_data: Dict[str, Any],
        request: ImageGenerationRequest
    ) -> ImageGenerationResponse:
        """解析图像生成响应

        Args:
            response_data: API 响应数据
            request: 原始请求对象

        Returns:
            ImageGenerationResponse: 标准化的图像生成响应
        """
        from ai_gen_hub.core.interfaces import ImageData

        images = []
        for img_data in response_data.get("data", []):
            image = ImageData(
                url=img_data.get("url"),
                b64_json=img_data.get("b64_json"),
                revised_prompt=img_data.get("revised_prompt")
            )
            images.append(image)

        return ImageGenerationResponse(
            created=response_data.get("created", int(time.time())),
            data=images
        )

    async def generate_text_optimized(
        self,
        request: "OptimizedTextGenerationRequest",
        api_key: Optional[str] = None
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """优化版本的文本生成实现

        支持v2接口的OptimizedTextGenerationRequest格式，
        由于Hugging Face使用OpenAI兼容格式，大部分参数可以直接使用。

        Args:
            request: 优化版本的文本生成请求
            api_key: API密钥，如果不提供则使用配置的密钥

        Returns:
            Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
                文本生成响应或流式响应迭代器
        """
        # 导入OptimizedTextGenerationRequest类型
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest

        # 获取API密钥
        if api_key is None:
            api_key = await self.key_manager.get_api_key(self.name)

        # 验证兼容性
        compatibility_report = self._compatibility_manager.validate_request_compatibility(
            request, "huggingface"
        )

        # 记录兼容性警告
        for warning in compatibility_report.get("warnings", []):
            self.logger.warning(f"Hugging Face兼容性警告: {warning}")

        # 如果有严重错误，抛出异常
        if not compatibility_report.get("compatible", True):
            errors = compatibility_report.get("errors", [])
            error_msg = f"请求与Hugging Face供应商不兼容: {'; '.join(errors)}"
            from ai_gen_hub.core.exceptions import InvalidRequestError
            raise InvalidRequestError(error_msg)

        # 使用兼容性管理器适配请求
        adapted_params = self._compatibility_manager.adapt_request_for_provider(
            request, "huggingface"
        )

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        if request.stream and request.stream.enabled:
            # 流式生成
            url = f"{self.base_url}/chat/completions"
            adapted_params["stream"] = True
            return self._handle_openai_stream_response_optimized(request, url, headers, adapted_params)
        else:
            # 非流式生成
            url = f"{self.base_url}/chat/completions"
            response = await self._make_request("POST", url, headers=headers, json_data=adapted_params)
            response_data = response.json()

            # 检查响应中是否有错误
            if "error" in response_data:
                error_msg = self._handle_openai_error(response_data)
                self.logger.error(f"Hugging Face API 返回错误: {error_msg}")
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)

            return self._parse_openai_response_optimized(response_data, request)

    async def _handle_openai_stream_response_optimized(
        self,
        request: "OptimizedTextGenerationRequest",
        url: str,
        headers: Dict[str, str],
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理优化版本的OpenAI格式流式响应"""
        try:
            async with self.http_client.stream("POST", url, headers=headers, json=request_data) as response:
                if response.status_code != 200:
                    error_data = await response.aread()
                    error_msg = f"Hugging Face API 流式请求失败: {response.status_code} - {error_data.decode()}"
                    self.logger.error(error_msg)
                    from ai_gen_hub.core.exceptions import APIError
                    raise APIError(error_msg)

                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk

                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)

                        if line.startswith("data: "):
                            data_str = line[6:]

                            if not data_str.strip() or data_str.strip() == "[DONE]":
                                continue

                            try:
                                data = json.loads(data_str)
                                stream_chunk = self._parse_openai_stream_chunk_optimized(data, request)
                                if stream_chunk:
                                    yield stream_chunk
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"解析流式响应数据失败: {e}, 数据: {data_str}")
                                continue

        except Exception as e:
            self.logger.error(f"处理 Hugging Face 优化版本流式响应时发生错误: {e}")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(f"流式响应处理失败: {e}")

    def _parse_openai_response_optimized(
        self,
        response_data: Dict[str, Any],
        request: "OptimizedTextGenerationRequest"
    ) -> TextGenerationResponse:
        """解析优化版本的OpenAI格式响应数据"""
        from ai_gen_hub.core.interfaces import TextGenerationRequest

        # 创建一个临时的传统请求对象用于解析
        temp_request = TextGenerationRequest(
            messages=request.messages,
            model=request.model
        )

        return self._parse_openai_response(response_data, temp_request)

    def _parse_openai_stream_chunk_optimized(
        self,
        chunk_data: Dict[str, Any],
        request: "OptimizedTextGenerationRequest"
    ) -> Optional[TextGenerationStreamChunk]:
        """解析优化版本的OpenAI格式流式响应块"""
        from ai_gen_hub.core.interfaces import TextGenerationRequest

        # 创建一个临时的传统请求对象用于解析
        temp_request = TextGenerationRequest(
            messages=request.messages,
            model=request.model
        )

        return self._parse_openai_stream_chunk(chunk_data, temp_request)
