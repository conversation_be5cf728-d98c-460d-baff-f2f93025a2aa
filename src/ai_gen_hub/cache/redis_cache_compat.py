"""
AI Gen Hub Redis缓存兼容实现

解决aioredis在Python 3.11中的兼容性问题，使用redis-py的异步功能
"""

import asyncio
import gzip
import json
import time
from typing import Any, List, Optional

try:
    # 尝试使用redis-py的异步功能
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
    REDIS_TYPE = "redis-py"
except ImportError:
    try:
        # 回退到aioredis（如果可用），但要处理Python 3.11+的TimeoutError冲突
        import sys
        if sys.version_info >= (3, 11):
            # Python 3.11+ 有内置的TimeoutError，可能与aioredis冲突
            # 暂时跳过aioredis，使用虚拟模块
            REDIS_AVAILABLE = False
            REDIS_TYPE = "none"
            redis = None
        else:
            import aioredis as redis
            REDIS_AVAILABLE = True
            REDIS_TYPE = "aioredis"
    except (ImportError, TypeError):
        # 如果都没有或有冲突，创建一个虚拟的redis模块
        REDIS_AVAILABLE = False
        REDIS_TYPE = "none"
        redis = None

from ai_gen_hub.cache.base import BaseCacheManager, CacheBackend, CacheStats
from ai_gen_hub.config.settings import RedisConfig
from ai_gen_hub.core.exceptions import CacheConnectionError


class CompatRedisCache(BaseCacheManager):
    """兼容的Redis缓存实现"""
    
    def __init__(
        self,
        redis_config: RedisConfig,
        default_ttl: int = 3600,
        key_prefix: str = "ai_gen_hub",
        compression_enabled: bool = True,
        compression_threshold: int = 1024  # 1KB
    ):
        """初始化Redis缓存
        
        Args:
            redis_config: Redis配置
            default_ttl: 默认TTL（秒）
            key_prefix: 键前缀
            compression_enabled: 是否启用压缩
            compression_threshold: 压缩阈值（字节）
        """
        super().__init__(CacheBackend.REDIS, default_ttl, key_prefix)
        
        self.redis_config = redis_config
        self.compression_enabled = compression_enabled
        self.compression_threshold = compression_threshold
        
        self._redis: Optional[Any] = None
        self._connection_lock = asyncio.Lock()
        self._available = REDIS_AVAILABLE
        
        # 统计信息
        self._local_stats = CacheStats()
        
        if not REDIS_AVAILABLE:
            self.logger.warning("Redis库不可用，Redis缓存功能将被禁用")
    
    async def _get_redis(self) -> Any:
        """获取Redis连接"""
        if not self._available:
            raise CacheConnectionError("Redis库不可用")
        
        if self._redis is None:
            async with self._connection_lock:
                if self._redis is None:
                    await self._connect()
        
        return self._redis
    
    async def _connect(self) -> None:
        """连接到Redis"""
        if not self._available:
            raise CacheConnectionError("Redis库不可用")
        
        try:
            if REDIS_TYPE == "redis-py":
                # 使用redis-py的异步客户端
                self._redis = redis.Redis.from_url(
                    self.redis_config.url,
                    password=self.redis_config.password,
                    db=self.redis_config.db,
                    decode_responses=False,  # 我们需要处理字节数据
                    max_connections=self.redis_config.pool_size,
                    socket_timeout=self.redis_config.pool_timeout,
                    socket_connect_timeout=self.redis_config.pool_timeout,
                )
            else:
                # 使用aioredis（如果可用）
                self._redis = redis.from_url(
                    self.redis_config.url,
                    password=self.redis_config.password,
                    db=self.redis_config.db,
                    decode_responses=False,
                    max_connections=self.redis_config.pool_size,
                    socket_timeout=self.redis_config.pool_timeout,
                    socket_connect_timeout=self.redis_config.pool_timeout,
                )
            
            # 测试连接
            await self._redis.ping()
            
            self.logger.info(
                "Redis连接成功",
                url=self.redis_config.url,
                db=self.redis_config.db,
                redis_type=REDIS_TYPE
            )
            
        except Exception as e:
            self.logger.error("Redis连接失败", error=str(e))
            raise CacheConnectionError(f"Redis连接失败: {e}")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._available:
            self._local_stats.misses += 1
            return None
        
        try:
            redis_client = await self._get_redis()
            
            # 获取原始数据
            raw_data = await redis_client.get(self._make_key(key))
            
            if raw_data is None:
                self._local_stats.misses += 1
                return None
            
            # 反序列化
            value = self._deserialize(raw_data)
            self._local_stats.hits += 1
            
            return value
            
        except Exception as e:
            self.logger.error("Redis获取失败", key=key, error=str(e))
            self._local_stats.misses += 1
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        if not self._available:
            return False
        
        try:
            redis_client = await self._get_redis()
            
            # 序列化
            serialized_data = self._serialize(value)
            
            # 设置TTL
            expire_time = ttl or self.default_ttl
            
            # 存储到Redis
            result = await redis_client.setex(
                self._make_key(key),
                expire_time,
                serialized_data
            )
            
            if result:
                self._local_stats.sets += 1
                return True
            
            return False
            
        except Exception as e:
            self.logger.error("Redis设置失败", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self._available:
            return False
        
        try:
            redis_client = await self._get_redis()
            result = await redis_client.delete(self._make_key(key))
            
            if result > 0:
                self._local_stats.deletes += 1
                return True
            
            return False
            
        except Exception as e:
            self.logger.error("Redis删除失败", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        if not self._available:
            return False
        
        try:
            redis_client = await self._get_redis()
            result = await redis_client.exists(self._make_key(key))
            return result > 0
            
        except Exception as e:
            self.logger.error("Redis存在检查失败", key=key, error=str(e))
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        if not self._available:
            return False
        
        try:
            redis_client = await self._get_redis()
            
            # 获取所有匹配的键
            pattern = f"{self.key_prefix}:*"
            keys = await redis_client.keys(pattern)
            
            if keys:
                await redis_client.delete(*keys)
            
            return True
            
        except Exception as e:
            self.logger.error("Redis清空失败", error=str(e))
            return False
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        return self._local_stats
    
    async def get_size(self) -> int:
        """获取缓存大小"""
        if not self._available:
            return 0
        
        try:
            redis_client = await self._get_redis()
            pattern = f"{self.key_prefix}:*"
            keys = await redis_client.keys(pattern)
            return len(keys)
            
        except Exception as e:
            self.logger.error("获取Redis大小失败", error=str(e))
            return 0
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的缓存键列表"""
        if not self._available:
            return []
        
        try:
            redis_client = await self._get_redis()
            full_pattern = f"{self.key_prefix}:{pattern}"
            keys = await redis_client.keys(full_pattern)
            
            # 移除前缀
            prefix_len = len(f"{self.key_prefix}:")
            return [key.decode('utf-8')[prefix_len:] if isinstance(key, bytes) else key[prefix_len:] for key in keys]
            
        except Exception as e:
            self.logger.error("获取Redis键列表失败", error=str(e))
            return []
    
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存键"""
        if not self._available:
            return 0
        
        try:
            redis_client = await self._get_redis()
            full_pattern = f"{self.key_prefix}:{pattern}"
            keys = await redis_client.keys(full_pattern)
            
            if keys:
                deleted = await redis_client.delete(*keys)
                self._local_stats.deletes += deleted
                return deleted
            
            return 0
            
        except Exception as e:
            self.logger.error("Redis模式删除失败", pattern=pattern, error=str(e))
            return 0
    
    def _serialize(self, value: Any) -> bytes:
        """序列化值"""
        try:
            # 序列化为JSON
            json_data = json.dumps(value, ensure_ascii=False, default=str)
            data = json_data.encode('utf-8')
            
            # 如果启用压缩且数据大于阈值，则压缩
            if self.compression_enabled and len(data) > self.compression_threshold:
                data = gzip.compress(data)
                # 添加压缩标记
                data = b'GZIP:' + data
            
            return data
            
        except Exception as e:
            self.logger.error("序列化失败", error=str(e))
            raise
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化值"""
        try:
            # 检查是否是压缩数据
            if data.startswith(b'GZIP:'):
                data = gzip.decompress(data[5:])
            
            # 反序列化JSON
            json_data = data.decode('utf-8')
            return json.loads(json_data)
            
        except Exception as e:
            self.logger.error("反序列化失败", error=str(e))
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self._redis:
            try:
                if hasattr(self._redis, 'close'):
                    await self._redis.close()
                elif hasattr(self._redis, 'aclose'):
                    await self._redis.aclose()
            except Exception as e:
                self.logger.warning("Redis连接关闭失败", error=str(e))
            finally:
                self._redis = None
        
        self.logger.info("Redis缓存已清理")


# 导出兼容的Redis缓存类
RedisCache = CompatRedisCache
